/** @format */

'use client';

import { colors } from '@/app/colors';
import { useCreditsData } from '@/app/shared/credits';
import { useProfileData } from '@/app/shared/profile';
import { useUserInteractionsContext } from '@/app/shared/userInteractions/shared/context/UserInteractionsProvider';
import { useSession } from 'next-auth/react';
import React, { useEffect, useState } from 'react';
import { FiCamera, FiLoader } from 'react-icons/fi';
import { ProfilePictureUpload } from './ProfilePictureUpload';

interface User {
	id: string;
	email: string;
	username: string;
	name?: string | null;
	image?: string | null;
	profile_picture_url?: string | null;
	role: 'user' | 'agent' | 'superuser';
	permissions: string[];
}

interface UserProfile {
	id: string;
	user_id: string;
	name: string;
	age: number;
	avatar_url?: string;
	profile_completed: boolean;
	allow_data_usage: boolean;
	preferences: Record<string, unknown>;
	timezone: string;
	total_locations_visited: number;
	total_reviews_written: number;
	total_photos_uploaded: number;
	adventurer_level: number;
	days_active: number;
	last_activity_date?: string;
	created_at: string;
	updated_at: string;
}

interface EnhancedProfileHeaderProps {
	user?: User;
	onProfileUpdate?: (updatedProfile: Partial<UserProfile>) => void;
	integrated?: boolean;
}

export const EnhancedProfileHeader: React.FC<EnhancedProfileHeaderProps> = ({
	user,
	onProfileUpdate,
	integrated = false,
}) => {
	const { data: session, update: updateSession } = useSession();
	const [showProfilePictureUpload, setShowProfilePictureUpload] =
		useState(false);

	// Use session user if no user prop provided
	const currentUser = user || session?.user;

	const { loading: profileLoading, loadProfile } = useProfileData({
		userId: currentUser?.id,
		autoLoad: true,
	});

	const { totalCredits, loading: creditsLoading } = useCreditsData({
		userId: currentUser?.id,
		autoLoad: true,
	});

	// Use the shared interaction context to get stats
	const { likes, favorites, visits, reviews, lazyLoadInteraction } =
		useUserInteractionsContext();

	// Load interaction data when component mounts
	useEffect(() => {
		if (currentUser?.id) {
			lazyLoadInteraction('likes');
			lazyLoadInteraction('favorites');
			lazyLoadInteraction('visits');
			lazyLoadInteraction('reviews');
		}
	}, [currentUser?.id, lazyLoadInteraction]);

	const interactionStatsLoading =
		likes.loading || favorites.loading || visits.loading || reviews.loading;

	if (profileLoading || creditsLoading || interactionStatsLoading) {
		return (
			<div
				className={
					integrated
						? 'py-4 md:py-6 lg:py-8'
						: 'border backdrop-blur-sm p-4 md:p-6 lg:p-8'
				}
				style={
					integrated
						? {}
						: {
								background: 'rgba(255, 255, 255, 0.9)',
								borderColor: colors.ui.gray200,
						  }
				}>
				<div className='flex items-center justify-center py-8 md:py-12'>
					<FiLoader
						className='w-8 h-8 md:w-12 md:h-12 animate-spin'
						style={{ color: colors.brand.blue }}
					/>
				</div>
			</div>
		);
	}

	if (integrated) {
		// Modern integrated mode - clean styling only
		return (
			<>
				{/* Modern Avatar Section */}
				<div className='relative'>
					<div className='relative group'>
						<img
							src={
								currentUser?.image ||
								`https://ui-avatars.com/api/?name=${encodeURIComponent(
									currentUser?.name || 'User'
								)}&background=${colors.brand.primary.replace(
									'#',
									''
								)}&color=fff`
							}
							alt={currentUser?.name || 'User'}
							className='w-24 h-24 md:w-32 md:h-32 lg:w-40 lg:h-40 rounded-2xl border-4 border-white shadow-xl object-cover transition-all duration-200 group-hover:scale-105'
						/>

						{/* Modern Upload Button */}
						<button
							onClick={() => setShowProfilePictureUpload(true)}
							className='absolute -bottom-2 -right-2 w-10 h-10 md:w-12 md:h-12 rounded-xl shadow-lg cursor-pointer transition-all duration-200 hover:scale-105 flex items-center justify-center'
							style={{ backgroundColor: colors.brand.primary }}>
							<FiCamera className='w-4 h-4 md:w-5 md:h-5 text-white' />
						</button>
					</div>
				</div>

				{/* Modern User Info Section */}
				<div className='flex flex-col gap-4 md:gap-6 mt-4 md:mt-6 lg:mt-8'>
					{/* Modern Name and User Info Card */}
					<div
						className='relative p-6 md:p-8 rounded-2xl border'
						style={{
							backgroundColor: 'white',
							borderColor: colors.ui.gray200,
						}}>
						<h1
							className='text-xl md:text-2xl lg:text-3xl font-bold mb-4 md:mb-6'
							style={{ color: colors.neutral.textBlack }}>
							{currentUser?.name || 'Anonymous User'}
						</h1>

						{/* User Details in modern grid */}
						<div className='grid grid-cols-1 md:grid-cols-2 gap-4 text-xs md:text-sm'>
							{(currentUser as any)?.username && (
								<div className='flex flex-col space-y-1'>
									<span
										className='text-xs uppercase tracking-wider font-medium'
										style={{ color: colors.neutral.slateGray }}>
										Username
									</span>
									<span
										className='font-semibold text-sm md:text-base'
										style={{ color: colors.neutral.textBlack }}>
										@{(currentUser as any).username}
									</span>
								</div>
							)}
							{currentUser?.email && (
								<div className='flex flex-col space-y-1'>
									<span
										className='text-xs uppercase tracking-wider font-medium'
										style={{ color: colors.neutral.slateGray }}>
										Email
									</span>
									<span
										className='font-semibold text-sm md:text-base'
										style={{ color: colors.neutral.textBlack }}>
										{currentUser.email}
									</span>
								</div>
							)}
							{(currentUser as any)?.age && (
								<div className='flex flex-col space-y-1'>
									<span
										className='text-xs uppercase tracking-wider font-medium'
										style={{ color: colors.neutral.slateGray }}>
										Age
									</span>
									<span
										className='font-semibold text-sm md:text-base'
										style={{ color: colors.neutral.textBlack }}>
										{(currentUser as any).age} years old
									</span>
								</div>
							)}
							{totalCredits > 0 && (
								<div className='flex flex-col space-y-1'>
									<span
										className='text-xs uppercase tracking-wider font-medium'
										style={{ color: colors.neutral.slateGray }}>
										Credits
									</span>
									<span
										className='font-semibold text-sm md:text-base'
										style={{ color: colors.brand.primary }}>
										{totalCredits} Credits
									</span>
								</div>
							)}
						</div>
					</div>
				</div>

				{/* Modern Profile Picture Upload Modal */}
				{showProfilePictureUpload && (
					<div className='fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50'>
						<div
							className='rounded-2xl shadow-2xl p-4 md:p-6 lg:p-8 max-w-lg w-full mx-4 max-h-[90vh] overflow-y-auto border'
							style={{
								backgroundColor: 'white',
								borderColor: colors.ui.gray200,
							}}>
							<div className='flex items-center justify-between mb-4 md:mb-6'>
								<h3
									className='text-lg md:text-xl lg:text-2xl font-bold'
									style={{ color: colors.neutral.textBlack }}>
									Update Profile Picture
								</h3>
								<button
									onClick={() => setShowProfilePictureUpload(false)}
									className='w-8 h-8 md:w-10 md:h-10 rounded-xl flex items-center justify-center transition-all duration-200'
									style={{
										backgroundColor: colors.ui.gray100,
										color: colors.neutral.slateGray,
									}}>
									<span className='text-lg md:text-xl'>✕</span>
								</button>
							</div>

							<ProfilePictureUpload
								currentProfilePicture={currentUser?.image || undefined}
								onUploadComplete={async (result) => {
									console.log('Profile picture uploaded:', result);
									onProfileUpdate?.({ avatar_url: result.url });
									setShowProfilePictureUpload(false);
									await updateSession();
									await loadProfile();
								}}
								onUploadError={(error) => {
									console.error('Profile picture upload error:', error);
									alert('Failed to upload profile picture: ' + error);
								}}
								size='large'
								showHistory={true}
								allowCrop={true}
							/>
						</div>
					</div>
				)}
			</>
		);
	}

	// Standalone mode - following design rules
	return (
		<div
			className='border backdrop-blur-sm shadow-lg overflow-hidden'
			style={{
				background: 'rgba(255, 255, 255, 0.9)',
				borderColor: colors.ui.gray200,
			}}>
			{/* Cover Background - Square Edges */}
			<div
				className='relative h-24 md:h-32 lg:h-40'
				style={{
					background: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`,
				}}>
				{/* Pattern */}
				<div className='absolute inset-0 opacity-10'>
					<div
						className='absolute inset-0'
						style={{
							backgroundImage: `radial-gradient(circle at 30% 30%, rgba(255,255,255,0.3) 0%, transparent 50%)`,
							backgroundSize: '60px 60px',
						}}></div>
				</div>
			</div>

			{/* Clean Profile Content */}
			<div className='relative p-6'>
				{/* Profile Header Row */}
				<div className='flex items-start -mt-12 mb-6'>
					{/* Clean Avatar Section */}
					<div className='relative'>
						<div className='relative group'>
							<img
								src={
									currentUser?.image ||
									`https://ui-avatars.com/api/?name=${encodeURIComponent(
										currentUser?.name || 'User'
									)}&background=${colors.brand.blue.replace('#', '')}&color=fff`
								}
								alt={currentUser?.name || 'User'}
								className='w-24 h-24 rounded-2xl border-4 border-white shadow-lg object-cover transition-transform duration-200 group-hover:scale-105'
							/>

							{/* Clean Avatar Upload Button */}
							<button
								onClick={() => setShowProfilePictureUpload(true)}
								className='absolute -bottom-1 -right-1 rounded-xl p-2 shadow-lg cursor-pointer transition-all duration-200 hover:scale-110'
								style={{ backgroundColor: colors.brand.blue }}>
								<FiCamera className='w-4 h-4 text-white' />
							</button>
						</div>
					</div>
				</div>

				{/* User Info */}
				<div>
					<h1
						className='text-2xl font-bold mb-2'
						style={{ color: colors.neutral.textBlack }}>
						{currentUser?.name || 'Anonymous User'}
					</h1>

					{/* Credits Badge */}
					{totalCredits > 0 && (
						<div
							className='inline-flex items-center gap-2 px-3 py-1 rounded-lg mb-4'
							style={{ backgroundColor: colors.ui.blue50 }}>
							<span
								className='text-sm font-medium'
								style={{ color: colors.brand.blue }}>
								{totalCredits} Credits
							</span>
						</div>
					)}
				</div>
			</div>

			{/* Enhanced Profile Picture Upload Modal */}
			{showProfilePictureUpload && (
				<div className='fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50'>
					<div className='bg-white rounded-3xl shadow-2xl p-8 max-w-lg w-full mx-4 max-h-[90vh] overflow-y-auto'>
						<div className='flex items-center justify-between mb-6'>
							<h3 className='text-2xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-cyan-600 bg-clip-text text-transparent'>
								Update Profile Picture
							</h3>
							<button
								onClick={() => setShowProfilePictureUpload(false)}
								className='w-10 h-10 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors'>
								<span className='text-gray-600 text-xl'>✕</span>
							</button>
						</div>

						<ProfilePictureUpload
							currentProfilePicture={currentUser?.image || undefined}
							onUploadComplete={async (result) => {
								console.log('Profile picture uploaded:', result);
								onProfileUpdate?.({ avatar_url: result.url });
								setShowProfilePictureUpload(false);
								await updateSession();
								await loadProfile();
							}}
							onUploadError={(error) => {
								console.error('Profile picture upload error:', error);
								alert('Failed to upload profile picture: ' + error);
							}}
							size='large'
							showHistory={true}
							allowCrop={true}
						/>
					</div>
				</div>
			)}
		</div>
	);
};
